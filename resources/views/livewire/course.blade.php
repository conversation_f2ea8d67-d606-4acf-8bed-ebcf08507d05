<?php

use Livewire\Volt\Component;

new class extends Component {
    public $code = "# 在这里编写 Python 代码\nprint('Hello, Python!')\n\n# 尝试一些数学运算\nimport math\nresult = math.sqrt(16)\nprint(f'平方根 16 = {result}')\n\n# 创建一个简单的列表\nnumbers = [1, 2, 3, 4, 5]\nprint(f'数字列表: {numbers}')\nprint(f'列表总和: {sum(numbers)}')";
    public $output = "";
    public $isRunning = false;

    public function runCode()
    {
        $this->isRunning = true;
        // 这里我们将使用前端的 Pyodide 来执行代码
        // 后端只是更新状态
        $this->dispatch("execute-python-code", code: $this->code);
    }

    public function updateOutput($output)
    {
        $this->output = $output;
        $this->isRunning = false;
    }

    public function clearOutput()
    {
        $this->output = "";
    }
}; ?>

<main class="flex">
    <div class="mx-auto max-w-7xl flex-1 p-6 px-4 sm:px-6 lg:p-8">
        <div class="-mx-2 sm:-mx-4">
            <div class="group aspect-video w-full rounded-2xl bg-gray-950 dark:bg-gray-900">
                <video
                    id="video"
                    src="https://assets.tailwindcss.com/templates/compass/liberation-from-regret.mp4"
                    poster="https://assets.tailwindcss.com/templates/compass/lesson-video-thumbnail-02.png"
                    preload="metadata"
                    controls=""
                    class="aspect-video w-full rounded-2xl sm:group-data-offscreen:data-playing:fixed sm:group-data-offscreen:data-playing:right-4 sm:group-data-offscreen:data-playing:bottom-4 sm:group-data-offscreen:data-playing:z-10 sm:group-data-offscreen:data-playing:max-w-md sm:group-data-offscreen:data-playing:rounded-xl sm:group-data-offscreen:data-playing:shadow-lg"
                ></video>
            </div>
        </div>
        <div class="mx-auto flex max-w-2xl gap-x-10 py-10 sm:py-14 lg:max-w-5xl">
            <div class="w-full flex-1">
                <div id="content" class="prose">
                    <h1 id="liberation-from-regret">Liberation from Regret</h1>
                    <p>If no alternative choice was ever possible, regret becomes logically unnecessary.</p>
                    <h2 id="the-futility-of-what-ifs">The Futility of What-Ifs</h2>
                    <p>Regret is one of the most insidious emotions we experience. It creeps into our minds and lingers like an unwelcome guest.</p>
                    <p>
                        You've spent hours torturing yourself with thoughts about what you "should have" done differently. That job you didn't take, that
                        relationship you mishandled, those words you wish you could take back. These regrets have wasted your mental energy for years.
                    </p>
                    <p>
                        But if your actions were inevitable—the only possible outcome of your genetics, past experiences, and circumstances—then regret makes no
                        sense. It's like regretting that water flows downhill or that the sun rises in the east.
                    </p>
                    <h2 id="the-anatomy-of-regret">The Anatomy of Regret</h2>
                    <p>
                        Regret depends on one key delusion: the belief that you could have done otherwise. When you regret a past action, you imagine an
                        alternative reality where you made a different "choice." This feels real, but it's exactly the illusion we've been dismantling.
                    </p>
                    <p>
                        Think about the job you turned down five years ago. When you regret this "decision," you're imagining a version of yourself who somehow
                        broke free from causality. But that version doesn't exist. The you who existed then, with that specific brain, those fears and desires,
                        that limited information, could not have acted differently. Declining the job was the only possible outcome.
                    </p>
                    <p>
                        Regret isn't just incorrect—it's nonsensical. It's like regretting that you can't fly by flapping your arms. The alternative simply
                        wasn't available.
                    </p>
                    <h2 id="why-regret-exists">Why Regret Exists</h2>
                    <p>
                        If regret is so irrational, why do we all feel it? Like many evolutionary adaptations, regret served a purpose despite being based on a
                        fiction.
                    </p>
                    <p>
                        Regret works as a crude learning mechanism. By creating the illusion that you could have done otherwise and attaching bad feelings to
                        certain outcomes, your brain tries to influence future behavior. It's clumsy but effective.
                    </p>
                    <p>
                        Society reinforces this because it's useful for maintaining order. The fiction that people "could have done otherwise" provides the
                        foundation for concepts like responsibility. These concepts, while false, help maintain social cohesion.
                    </p>
                    <p>
                        But now that you understand determinism, you can learn from past outcomes without the useless suffering that typically comes with
                        regret.
                    </p>
                    <h2 id="practical-alternatives-to-regret">Practical Alternatives to Regret</h2>
                    <h3 id="from-regret-to-recognition">From Regret to Recognition</h3>
                    <p>
                        Instead of regretting past actions, simply recognize them as inevitable outcomes of who you were and the circumstances you faced. That
                        embarrassing comment you made at the office party wasn't a "mistake" you should punish yourself for—it was the unavoidable product of
                        your fatigue, the three glasses of wine, and your social anxiety.
                    </p>
                    <p>
                        This recognition doesn't prevent learning. You can observe the outcome, note the causes, and let this information become part of what
                        determines your future behavior. "When I drink at social events, certain predictable outcomes occur. This observation now becomes part
                        of my programming."
                    </p>
                    <p>
                        The key difference is that this learning happens without the suffering of regret. You're simply updating your internal models based on
                        observed outcomes, like a scientist recording experimental results.
                    </p>
                    <h3 id="curious-observation-instead-of-blame">Curious Observation Instead of Blame</h3>
                    <p>
                        Rather than asking "Why didn't I take that job?" (which assumes you could have), ask "What factors caused me to decline that offer?"
                        This shifts from useless regret to interesting self-observation.
                    </p>
                    <p>
                        You might discover that your decision was influenced by an unconscious fear of success, a misunderstanding of the opportunity, or the
                        influence of someone who is no longer in your life. This exploration isn't about blame but about understanding the mechanisms that
                        produced the inevitable outcome.
                    </p>
                    <p>
                        This curiosity helps you map the factors that determine your behavior, potentially altering how similar situations unfold in the
                        future—not because you're "choosing differently," but because the inputs have changed.
                    </p>
                    <h3 id="case-study-the-failed-relationship">Case Study: The Failed Relationship</h3>
                    <p>
                        Consider Maria, who spent years regretting her behavior in a previous relationship. She repeatedly told herself she "should have been
                        more communicative" and "shouldn't have taken him for granted." These regrets caused suffering without producing any useful change.
                    </p>
                    <p>
                        After embracing determinism, Maria's perspective changed. She recognized that her behavior in the relationship was the inevitable result
                        of her attachment style (formed in childhood), her models of relationships (inherited from her parents), and specific stressors present
                        during that period. Her behavior wasn't a "choice" she made poorly—it was the only possible outcome given who she was at that time.
                    </p>
                    <p>
                        This recognition didn't prevent learning. Maria could observe the patterns that emerged and how they contributed to the relationship's
                        end. This information became part of what determined her behavior in later relationships. But importantly, this learning happened
                        without the useless suffering of believing she "could have" saved the relationship if only she had "chosen" differently.
                    </p>
                    <h2 id="the-benefits-of-abandoning-regret">The Benefits of Abandoning Regret</h2>
                    <h3 id="freedom-from-the-past">Freedom from the Past</h3>
                    <p>
                        When you truly understand that regret is illogical, you experience a profound freedom. The past no longer looks like a series of
                        mistakes and missed opportunities but as the only possible unfolding given all factors involved. This realization frees up enormous
                        mental energy previously wasted on pointless what-if thinking.
                    </p>
                    <p>
                        This freedom extends to how you see others too. The parent who failed you, the partner who betrayed you, the friend who disappeared when
                        you needed them—all were acting out their inevitable programming. Their behavior, like yours, was the only possible outcome given their
                        nature and circumstances.
                    </p>
                    <p>
                        This doesn't mean staying in harmful situations. It simply means recognizing that blaming people for past actions is like blaming a
                        calculator for the result of a math problem. You can still replace the calculator if it's not serving your purposes.
                    </p>
                    <h3 id="better-future-outcomes">Better Future Outcomes</h3>
                    <p>
                        Strangely, abandoning regret can actually improve future outcomes. When you stop wasting energy on useless regret, you have more
                        resources available for observation, analysis, and adaptation.
                    </p>
                    <p>
                        Moreover, regret itself often leads to negative outcomes. The person who regrets a failed business venture becomes afraid of risks in
                        ways that limit future opportunities. The person who regrets a failed relationship becomes defensive in ways that harm new connections.
                        By removing regret from the equation, you potentially alter future outcomes—not through "free choice," but through the modification of
                        inputs.
                    </p>
                    <h2 id="moving-forward-without-looking-back">Moving Forward Without Looking Back</h2>
                    <p>
                        As you continue through life, practice recognizing regret when it appears. Notice how absurd it is to wish you could have broken the
                        laws of causality. Observe how the feeling still emerges despite your understanding—this is simply your evolved psychology running its
                        programs.
                    </p>
                    <p>
                        When you catch yourself thinking "I should have done X," translate this thought: "Given who I was and the circumstances I faced, doing X
                        was not possible. What happened was the only possible outcome."
                    </p>
                    <p>
                        This isn't just a word game—it fundamentally changes your relationship with the past. Rather than seeing your history as a series of
                        failures, you can recognize it as the only possible unfolding of your particular set of causes and conditions.
                    </p>
                    <h2 id="next-steps">Next Steps</h2>
                    <p>
                        In our next lesson, "Recognizing Patterns in Past Decisions," we'll explore how your apparent failures weren't freely chosen but were
                        inevitable given your circumstances. We'll examine how identifying the causal patterns in your past behavior can help you understand
                        your predetermined tendencies, not to "choose differently" but to observe your programming with greater clarity.
                    </p>
                    <p>
                        Remember: You didn't choose to feel regret any more than you chose the actions you regret. Both were inevitable. And your freedom from
                        regret, should it occur, will be equally predetermined. Isn't that a relief?
                    </p>
                </div>
                <div class="mt-16 border-t border-gray-200 pt-8 dark:border-white/10">
                    <div class="flow-root">
                        <a class="-mx-3 -my-2 block rounded-xl px-3 py-2 hover:bg-gray-950/4 dark:hover:bg-white/5" href="/recognizing-patterns">
                            <p class="flex items-center gap-3 text-sm/7 text-gray-500">
                                Up next
                                <svg viewBox="0 0 5 8" fill="none" class="h-2 shrink-0 stroke-current">
                                    <path d="M1 7.5L4 4.5L1 1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                </svg>
                            </p>
                            <p class="mt-3 text-base/7 font-medium text-gray-950 dark:text-white">Recognizing Patterns</p>
                            <p class="text-sm/7 text-gray-700 dark:text-gray-400">
                                Failures weren't freely chosen but were inevitable given your circumstances.
                            </p>
                        </a>
                    </div>
                </div>
            </div>
            <div class="hidden w-66 lg:block">
                <nav class="sticky top-16">
                    <h2 class="text-sm/6 font-semibold text-gray-950 dark:text-white">On this page</h2>
                    <ul class="mt-3 flex flex-col gap-3 border-l border-gray-950/10 text-sm/6 text-gray-700 dark:border-white/10 dark:text-gray-400">
                        <li
                            class="-ml-px border-l border-transparent pl-4 hover:text-gray-950 hover:not-has-aria-[current=location]:border-gray-400 has-aria-[current=location]:border-gray-950 dark:hover:text-white dark:has-aria-[current=location]:border-white"
                        >
                            <a
                                href="#the-futility-of-what-ifs"
                                class="block aria-[current=location]:font-medium aria-[current=location]:text-gray-950 dark:aria-[current=location]:text-white"
                                aria-current="location"
                            >
                                The Futility of What-Ifs
                            </a>
                        </li>
                        <li
                            class="-ml-px border-l border-transparent pl-4 hover:text-gray-950 hover:not-has-aria-[current=location]:border-gray-400 has-aria-[current=location]:border-gray-950 dark:hover:text-white dark:has-aria-[current=location]:border-white"
                        >
                            <a
                                href="#the-anatomy-of-regret"
                                class="block aria-[current=location]:font-medium aria-[current=location]:text-gray-950 dark:aria-[current=location]:text-white"
                            >
                                The Anatomy of Regret
                            </a>
                        </li>
                        <li
                            class="-ml-px border-l border-transparent pl-4 hover:text-gray-950 hover:not-has-aria-[current=location]:border-gray-400 has-aria-[current=location]:border-gray-950 dark:hover:text-white dark:has-aria-[current=location]:border-white"
                        >
                            <a
                                href="#why-regret-exists"
                                class="block aria-[current=location]:font-medium aria-[current=location]:text-gray-950 dark:aria-[current=location]:text-white"
                            >
                                Why Regret Exists
                            </a>
                        </li>
                        <li
                            class="-ml-px border-l border-transparent pl-4 hover:text-gray-950 hover:not-has-aria-[current=location]:border-gray-400 has-aria-[current=location]:border-gray-950 dark:hover:text-white dark:has-aria-[current=location]:border-white"
                        >
                            <a
                                href="#practical-alternatives-to-regret"
                                class="block aria-[current=location]:font-medium aria-[current=location]:text-gray-950 dark:aria-[current=location]:text-white"
                            >
                                Practical Alternatives to Regret
                            </a>
                        </li>
                        <li
                            class="-ml-px border-l border-transparent pl-4 hover:text-gray-950 hover:not-has-aria-[current=location]:border-gray-400 has-aria-[current=location]:border-gray-950 dark:hover:text-white dark:has-aria-[current=location]:border-white"
                        >
                            <a
                                href="#from-regret-to-recognition"
                                class="block pl-4 aria-[current=location]:font-medium aria-[current=location]:text-gray-950 dark:aria-[current=location]:text-white"
                            >
                                From Regret to Recognition
                            </a>
                        </li>
                        <li
                            class="-ml-px border-l border-transparent pl-4 hover:text-gray-950 hover:not-has-aria-[current=location]:border-gray-400 has-aria-[current=location]:border-gray-950 dark:hover:text-white dark:has-aria-[current=location]:border-white"
                        >
                            <a
                                href="#curious-observation-instead-of-blame"
                                class="block pl-4 aria-[current=location]:font-medium aria-[current=location]:text-gray-950 dark:aria-[current=location]:text-white"
                            >
                                Curious Observation Instead of Blame
                            </a>
                        </li>
                        <li
                            class="-ml-px border-l border-transparent pl-4 hover:text-gray-950 hover:not-has-aria-[current=location]:border-gray-400 has-aria-[current=location]:border-gray-950 dark:hover:text-white dark:has-aria-[current=location]:border-white"
                        >
                            <a
                                href="#case-study-the-failed-relationship"
                                class="block pl-4 aria-[current=location]:font-medium aria-[current=location]:text-gray-950 dark:aria-[current=location]:text-white"
                            >
                                Case Study: The Failed Relationship
                            </a>
                        </li>
                        <li
                            class="-ml-px border-l border-transparent pl-4 hover:text-gray-950 hover:not-has-aria-[current=location]:border-gray-400 has-aria-[current=location]:border-gray-950 dark:hover:text-white dark:has-aria-[current=location]:border-white"
                        >
                            <a
                                href="#the-benefits-of-abandoning-regret"
                                class="block aria-[current=location]:font-medium aria-[current=location]:text-gray-950 dark:aria-[current=location]:text-white"
                            >
                                The Benefits of Abandoning Regret
                            </a>
                        </li>
                        <li
                            class="-ml-px border-l border-transparent pl-4 hover:text-gray-950 hover:not-has-aria-[current=location]:border-gray-400 has-aria-[current=location]:border-gray-950 dark:hover:text-white dark:has-aria-[current=location]:border-white"
                        >
                            <a
                                href="#freedom-from-the-past"
                                class="block pl-4 aria-[current=location]:font-medium aria-[current=location]:text-gray-950 dark:aria-[current=location]:text-white"
                            >
                                Freedom from the Past
                            </a>
                        </li>
                        <li
                            class="-ml-px border-l border-transparent pl-4 hover:text-gray-950 hover:not-has-aria-[current=location]:border-gray-400 has-aria-[current=location]:border-gray-950 dark:hover:text-white dark:has-aria-[current=location]:border-white"
                        >
                            <a
                                href="#better-future-outcomes"
                                class="block pl-4 aria-[current=location]:font-medium aria-[current=location]:text-gray-950 dark:aria-[current=location]:text-white"
                            >
                                Better Future Outcomes
                            </a>
                        </li>
                        <li
                            class="-ml-px border-l border-transparent pl-4 hover:text-gray-950 hover:not-has-aria-[current=location]:border-gray-400 has-aria-[current=location]:border-gray-950 dark:hover:text-white dark:has-aria-[current=location]:border-white"
                        >
                            <a
                                href="#moving-forward-without-looking-back"
                                class="block aria-[current=location]:font-medium aria-[current=location]:text-gray-950 dark:aria-[current=location]:text-white"
                            >
                                Moving Forward Without Looking Back
                            </a>
                        </li>
                        <li
                            class="-ml-px border-l border-transparent pl-4 hover:text-gray-950 hover:not-has-aria-[current=location]:border-gray-400 has-aria-[current=location]:border-gray-950 dark:hover:text-white dark:has-aria-[current=location]:border-white"
                        >
                            <a
                                href="#next-steps"
                                class="block aria-[current=location]:font-medium aria-[current=location]:text-gray-950 dark:aria-[current=location]:text-white"
                            >
                                Next Steps
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <flux:sidebar
        sticky
        class="bg-zinc-800 text-white [:where(&)]:w-96"
        x-data="pythonRunner()"
        x-init="initPyodide()"
        @execute-python-code.window="executeCode($event.detail.code)"
    >
        <div class="border-b border-zinc-700 p-4">
            <div class="mb-2 flex items-center justify-between">
                <h3 class="text-lg font-semibold text-white">Python 代码运行器</h3>
                <div class="flex items-center gap-2">
                    <div x-show="!pyodideReady" class="flex items-center gap-1 text-xs text-yellow-400">
                        <svg class="h-3 w-3 animate-spin" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path
                                class="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                        </svg>
                        加载中
                    </div>
                    <div x-show="pyodideReady" class="flex items-center gap-1 text-xs text-green-400">
                        <svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                clip-rule="evenodd"
                            ></path>
                        </svg>
                        就绪
                    </div>
                </div>
            </div>
            <p class="text-sm text-zinc-300">在下方编写 Python 代码并点击运行 (Ctrl+Enter)</p>
        </div>

        {{-- 代码编辑器 --}}
        <div class="flex flex-1 flex-col">
            <div class="border-b border-zinc-700 p-4">
                <label class="mb-2 block text-sm font-medium text-zinc-300">Python 代码:</label>
                <textarea
                    wire:model.live.debounce.500ms="code"
                    class="h-48 w-full resize-none rounded-lg border border-zinc-600 bg-zinc-900 p-3 font-mono text-sm text-white focus:border-transparent focus:ring-2 focus:ring-blue-500"
                    placeholder="# 在这里编写 Python 代码...&#10;# 按 Ctrl+Enter 快速运行"
                    x-ref="codeEditor"
                    @keydown.ctrl.enter="$wire.runCode()"
                    @keydown.cmd.enter="$wire.runCode()"
                ></textarea>
            </div>

            {{-- 控制按钮 --}}
            <div class="border-b border-zinc-700 p-4">
                <div class="mb-3 flex gap-2">
                    <flux:button wire:click="runCode" :disabled="$isRunning" variant="primary" size="sm" class="flex-1">
                        <span x-show="!$wire.isRunning">▶ 运行代码</span>
                        <span x-show="$wire.isRunning" class="flex items-center gap-2">
                            <svg class="h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path
                                    class="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                            </svg>
                            运行中...
                        </span>
                    </flux:button>

                    <flux:button wire:click="clearOutput" variant="ghost" size="sm" class="text-zinc-400 hover:text-white">🗑 清空</flux:button>
                </div>

                {{-- 示例代码按钮 --}}
                <div class="space-y-2">
                    <p class="text-xs text-zinc-400">快速示例:</p>
                    <div class="flex flex-wrap gap-1">
                        <button
                            @click="loadExample('hello')"
                            class="rounded bg-zinc-700 px-2 py-1 text-xs text-zinc-300 transition-colors hover:bg-zinc-600 hover:text-white"
                        >
                            Hello World
                        </button>
                        <button
                            @click="loadExample('math')"
                            class="rounded bg-zinc-700 px-2 py-1 text-xs text-zinc-300 transition-colors hover:bg-zinc-600 hover:text-white"
                        >
                            数学运算
                        </button>
                        <button
                            @click="loadExample('list')"
                            class="rounded bg-zinc-700 px-2 py-1 text-xs text-zinc-300 transition-colors hover:bg-zinc-600 hover:text-white"
                        >
                            列表操作
                        </button>
                        <button
                            @click="loadExample('function')"
                            class="rounded bg-zinc-700 px-2 py-1 text-xs text-zinc-300 transition-colors hover:bg-zinc-600 hover:text-white"
                        >
                            函数定义
                        </button>
                    </div>
                </div>
            </div>

            {{-- 输出结果 --}}
            <div class="flex-1 p-4">
                <label class="mb-2 block text-sm font-medium text-zinc-300">运行结果:</label>
                <div class="h-48 overflow-y-auto rounded-lg border border-zinc-600 bg-zinc-900 p-3">
                    <div x-show="!pyodideReady" class="text-sm text-zinc-400">
                        <div class="flex items-center gap-2">
                            <svg class="h-4 w-4 animate-spin" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path
                                    class="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                ></path>
                            </svg>
                            正在加载 Python 环境...
                        </div>
                    </div>

                    <div x-show="pyodideReady && !output && !$wire.output" class="text-sm text-zinc-500 italic">点击"运行代码"查看输出结果</div>

                    <pre
                        x-show="output || $wire.output"
                        class="font-mono text-sm whitespace-pre-wrap text-green-400"
                        x-text="output || $wire.output"
                    ></pre>
                </div>
            </div>
        </div>
    </flux:sidebar>
</main>

{{-- 加载 Pyodide CDN --}}
<script src="https://cdn.jsdelivr.net/pyodide/v0.24.1/full/pyodide.js"></script>

<script>
    // 在 Alpine.js 初始化之前定义数据
    document.addEventListener('alpine:init', () => {
        Alpine.data('pythonRunner', () => ({
            pyodide: null,
            pyodideReady: false,
            output: '',

            // 示例代码
            examples: {
                hello: `# Hello World 示例
    print("Hello, Python!")
    print("欢迎使用 Python 代码运行器！")

    # 显示当前时间
    import datetime
    now = datetime.datetime.now()
    print(f"当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")`,

                math: `# 数学运算示例
    import math

    # 基本运算
    a = 10
    b = 3
    print(f"{a} + {b} = {a + b}")
    print(f"{a} - {b} = {a - b}")
    print(f"{a} * {b} = {a * b}")
    print(f"{a} / {b} = {a / b:.2f}")

    # 数学函数
    print(f"√{a} = {math.sqrt(a):.2f}")
    print(f"sin(π/2) = {math.sin(math.pi/2)}")
    print(f"2^3 = {2**3}")`,

                list: `# 列表操作示例
    # 创建列表
    fruits = ['苹果', '香蕉', '橙子', '葡萄']
    numbers = [1, 2, 3, 4, 5]

    print("水果列表:", fruits)
    print("数字列表:", numbers)

    # 列表操作
    fruits.append('草莓')
    print("添加草莓后:", fruits)

    # 列表推导式
    squares = [x**2 for x in numbers]
    print("平方数:", squares)

    # 统计函数
    print(f"数字总和: {sum(numbers)}")
    print(f"最大值: {max(numbers)}")
    print(f"最小值: {min(numbers)}")`,

                function: `# 函数定义示例
    def greet(name, age=None):
        """问候函数"""
        if age:
            return f"你好，{name}！你今年{age}岁。"
        else:
            return f"你好，{name}！"

    def calculate_area(length, width):
        """计算矩形面积"""
        return length * width

    def fibonacci(n):
        """计算斐波那契数列"""
        if n <= 1:
            return n
        return fibonacci(n-1) + fibonacci(n-2)

    # 使用函数
    print(greet("小明"))
    print(greet("小红", 18))

    area = calculate_area(5, 3)
    print(f"矩形面积: {area}")

    print("斐波那契数列前6项:")
    for i in range(6):
        print(f"F({i}) = {fibonacci(i)}")`,
            },

            async initPyodide() {
                try {
                    console.log('开始加载 Pyodide...');

                    // 等待 Pyodide 脚本加载完成
                    if (typeof loadPyodide === 'undefined') {
                        this.output = '正在加载 Python 环境，请稍候...';
                        // 等待 loadPyodide 函数可用
                        await new Promise((resolve) => {
                            const checkPyodide = () => {
                                if (typeof loadPyodide !== 'undefined') {
                                    resolve();
                                } else {
                                    setTimeout(checkPyodide, 100);
                                }
                            };
                            checkPyodide();
                        });
                    }

                    // 加载 Pyodide
                    this.pyodide = await loadPyodide({
                        indexURL: 'https://cdn.jsdelivr.net/pyodide/v0.24.1/full/',
                        stdout: (text) => {
                            this.output += text;
                        },
                        stderr: (text) => {
                            this.output += 'Error: ' + text;
                        },
                    });

                    console.log('Pyodide 加载完成');
                    this.pyodideReady = true;
                    this.output = 'Python 环境已准备就绪！\n';

                    // 预加载一些常用的包（可选）
                    try {
                        await this.pyodide.loadPackage(['numpy']);
                        console.log('NumPy 包加载完成');
                    } catch (error) {
                        console.log('NumPy 包加载失败，但基础功能仍可使用');
                    }
                } catch (error) {
                    console.error('Pyodide 加载失败:', error);
                    this.output = 'Python 环境加载失败: ' + error.message;
                }
            },

            async executeCode(code) {
                if (!this.pyodideReady) {
                    this.output = 'Python 环境尚未准备就绪，请稍候...';
                    return;
                }

                if (!code.trim()) {
                    this.output = '请输入要执行的 Python 代码';
                    return;
                }

                try {
                    // 清空之前的输出
                    this.output = '';

                    // 执行 Python 代码
                    const result = this.pyodide.runPython(code);

                    // 如果有返回值且不是 None，显示返回值
                    if (result !== undefined && result !== null && result.toString() !== 'None') {
                        this.output += '\n>>> ' + result.toString();
                    }

                    // 如果没有任何输出，显示执行完成信息
                    if (!this.output.trim()) {
                        this.output = '代码执行完成（无输出）';
                    }

                    // 更新 Livewire 组件的输出
                    this.$wire.updateOutput(this.output);
                } catch (error) {
                    console.error('Python 代码执行错误:', error);
                    this.output = 'Python 执行错误:\n' + error.message;
                    this.$wire.updateOutput(this.output);
                }
            },

            // 加载示例代码
            loadExample(type) {
                if (this.examples[type]) {
                    this.$wire.set('code', this.examples[type]);
                    this.output = '';
                    this.$wire.updateOutput('');
                }
            },
        }));
    });
</script>
